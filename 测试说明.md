# 弹幕实时保存功能测试说明

## 测试步骤

### 1. 启动项目
```bash
npm run dev
```

### 2. 测试自动保存功能

1. **连接直播间**
   - 在房间号输入框中输入一个有效的抖音直播间号
   - 点击连接按钮

2. **验证自动启用**
   - 连接成功后，左侧录制图标应该自动亮起（红色高亮）
   - 控制台应该显示"实时弹幕保存已自动启用"

3. **选择保存位置**
   - 如果浏览器支持 File System Access API，会自动弹出文件保存对话框
   - 文件名默认为房间号（如：123456789.txt）
   - 选择保存位置并确认

4. **验证弹幕保存**
   - 观察直播间弹幕
   - 检查保存的文件，应该实时出现新的弹幕记录
   - 格式应该为：`用户名: 弹幕内容`

### 3. 测试降级模式

1. **模拟不支持的浏览器**
   - 在开发者工具中执行：`delete window.showSaveFilePicker`
   - 重新连接直播间

2. **验证内存缓存**
   - 录制图标应该仍然亮起
   - 控制台显示使用内存缓存方式

3. **下载缓存文件**
   - 点击录制图标
   - 应该自动下载包含弹幕的txt文件

### 4. 测试追加写入

1. **使用已存在的文件**
   - 选择一个已经存在的弹幕文件
   - 新弹幕应该追加到文件末尾，不覆盖原有内容

## 预期结果

### 文件格式示例
```
用户A: 主播好！
用户B: 666
匿名用户: 支持主播
用户C: 点赞点赞
```

### 功能验证点

- ✅ 连接成功后自动启用实时保存
- ✅ 录制图标正确显示状态（亮起/暗淡）
- ✅ 只保存聊天类型弹幕，过滤其他消息
- ✅ 文件格式纯净，无时间戳和注释
- ✅ 支持追加写入模式
- ✅ 浏览器不支持时自动降级到内存缓存
- ✅ 断开连接时正确清理资源

## 常见问题

### Q: 为什么没有弹出文件保存对话框？
A: 可能是浏览器不支持 File System Access API，系统会自动降级到内存缓存模式。

### Q: 文件中没有弹幕记录？
A: 检查是否有聊天类型的弹幕，系统只保存 WebcastChatMessage 类型的消息。

### Q: 如何查看保存状态？
A: 观察左侧录制图标，亮起表示已启用，同时控制台会有相关日志。

## 技术细节

- 使用 File System Access API 进行文件操作
- 支持 `keepExistingData: true` 追加写入模式
- 降级方案使用 Blob + a标签下载
- 实时写入，无需缓冲
