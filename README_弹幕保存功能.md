# 弹幕实时保存功能说明

## 功能概述

新增了实时保存弹幕到本地文件的功能，可以在直播过程中实时将聊天弹幕保存到txt文件中。

## 主要特性

1. **实时保存**: 每当收到新的聊天弹幕时，立即保存到文件
2. **文件命名**: 自动以房间ID和时间戳命名文件，格式为 `房间ID_yyyy-MM-dd_HHmmss.txt`
3. **仅保存聊天**: 只保存 `WebcastChatMessage` 类型的弹幕，过滤其他类型消息
4. **格式化输出**: 弹幕以 `[时间] 用户名: 弹幕内容` 的格式保存
5. **文件头信息**: 包含房间信息、主播信息、开始时间等元数据
6. **自动关闭**: 连接断开时自动关闭文件并写入结束信息

## 使用方法

### 1. 启用实时保存

1. 连接到直播间
2. 点击左侧工具栏中的录制图标（🔴）
3. 选择保存文件的位置和文件名
4. 开始实时保存弹幕

### 2. 停止实时保存

1. 再次点击录制图标
2. 或者断开直播间连接（会自动停止）

## 技术实现

### 核心修改

1. **DyCast类增强**:
   - 添加了文件保存相关属性
   - 新增 `enableSaveToFile()` 和 `disableSaveToFile()` 方法
   - 在 `_dealMessages()` 中添加实时保存逻辑

2. **前端界面**:
   - 新增实时保存按钮
   - 添加状态指示（激活时高亮显示）
   - 集成用户交互逻辑

### 文件格式示例

```
# 抖音直播弹幕记录
# 房间号: 123456789
# 房间ID: 7234567890123456789
# 主播: 主播昵称
# 开始时间: 2024-01-01 20:30:00
# ==========================================

[20:30:15] 用户A: 主播好！
[20:30:18] 用户B: 666
[20:30:22] 用户C: 支持主播
...

# ==========================================
# 结束时间: 2024-01-01 21:30:00
# 弹幕记录结束
```

## 浏览器兼容性

- 需要支持 File System Access API 的现代浏览器
- 推荐使用 Chrome 86+ 或 Edge 86+
- 需要在 HTTPS 环境或 localhost 下运行

## 注意事项

1. 文件保存功能依赖浏览器的 File System Access API
2. 只有聊天类型的弹幕会被保存到文件
3. 文件会实时写入，无需等待连接结束
4. 建议在稳定的网络环境下使用，避免频繁重连影响文件完整性

## 错误处理

- 如果浏览器不支持 File System Access API，会显示相应提示
- 文件写入出错时会在控制台记录错误信息
- 连接断开时会自动清理文件资源
