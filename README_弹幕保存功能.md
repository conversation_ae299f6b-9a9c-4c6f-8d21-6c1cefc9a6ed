# 弹幕实时保存功能说明

## 功能概述

新增了实时保存弹幕到本地文件的功能，连接直播间后自动开启，实时将聊天弹幕保存到txt文件中。

## 主要特性

1. **自动启用**: 连接直播间后自动开启实时保存，无需手动操作
2. **简洁文件名**: 文件名直接为房间号，如 `123456789.txt`
3. **追加写入**: 如果文件已存在，直接追加新弹幕，不覆盖历史记录
4. **仅保存聊天**: 只保存 `WebcastChatMessage` 类型的弹幕，过滤其他类型消息
5. **纯净格式**: 弹幕以 `用户名: 弹幕内容` 的简洁格式保存，无时间戳和注释
6. **兼容降级**: 支持现代浏览器的 File System Access API，不支持时自动降级到传统下载方式

## 使用方法

### 自动保存流程

1. 连接到直播间
2. 系统自动启用实时保存（左侧录制图标会亮起）
3. 选择保存文件的位置（首次连接时）
4. 弹幕自动实时保存到文件

### 状态指示

- 🔴 **亮起**: 实时保存已启用
- 🔴 **暗淡**: 实时保存未启用

## 技术实现

### 核心修改

1. **DyCast类增强**:
   - 添加了文件保存相关属性
   - 新增 `enableSaveToFile()` 和 `disableSaveToFile()` 方法
   - 在 `_dealMessages()` 中添加实时保存逻辑

2. **前端界面**:
   - 新增实时保存按钮
   - 添加状态指示（激活时高亮显示）
   - 集成用户交互逻辑

### 文件格式示例

```
用户A: 主播好！
用户B: 666
用户C: 支持主播
匿名用户: 哈哈哈
用户D: 点赞点赞
...
```

## 浏览器兼容性

- 需要支持 File System Access API 的现代浏览器
- 推荐使用 Chrome 86+ 或 Edge 86+
- 需要在 HTTPS 环境或 localhost 下运行

## 注意事项

1. **自动启用**: 连接直播间后自动启用，无需手动操作
2. **文件追加**: 如果选择已存在的文件，新弹幕会追加到文件末尾
3. **纯净保存**: 只保存用户名和弹幕内容，无时间戳和其他信息
4. **兼容性**: 优先使用现代 API，不支持时自动降级到传统下载方式
5. **实时写入**: 弹幕实时写入文件，无需等待连接结束

## 错误处理

- 如果浏览器不支持 File System Access API，自动降级到内存缓存模式
- 降级模式下，连接断开时会自动下载缓存的弹幕文件
- 文件写入出错时会在控制台记录错误信息，但不影响弹幕接收
